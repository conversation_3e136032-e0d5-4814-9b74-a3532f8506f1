--(1),标准打坐alias两套，stardard_dazuo ,standard_dazuo_2
----------------------------------------------------------------
add_alias("standard_dazuo", function(params) --标准打坐alias 有lian force
	local maxqi = var["maxqi"] or 100
	local skills = var["skills_level"] or {}
	local jifa_force = var["jifa_force"] or "none"
	local force_level = skills[jifa_force] or 0
	local do_stop = var["do_stop"] or 0
	local maxpot = var["maxpot"] or 100
	maxpot = maxpot - 99
	if do_stop == 0 then
		local lian_force = var["lian_force"] or 1
		local myexp = var["exp"] or 150000
		if tonumber(lian_force) == 1 and force_level < maxpot then
			if tonumber(myexp) < 200000 then
				exec("lian force 2;yun jing;yun qi;hp " .. var["char_id"])
			elseif tonumber(myexp) < 6000000 then
				exec("lian force 4;yun jing;yun qi;hp " .. var["char_id"])
			elseif tonumber(myexp) > 20000000 and tonumber(myexp) <= 50000000 then
				exec("lian force 20;yun jing;yun qi;hp " .. var["char_id"])
			elseif tonumber(myexp) > 50000000 then
				exec("lian force 50;yun jing;yun qi;hp " .. var["char_id"])
			else
				exec("lian force 8;yun jing;yun qi;hp " .. var["char_id"])
			end
		else
			exec("yun jing;yun qi;hp " .. var["char_id"])
		end

		local dazuo = var["dazuo"] or maxqi / 4 or 1000
		if var["say"] and var["say"] ~= "" then -- 打坐之前可以执行一些命令var["say"]，比如读书,或纯说话
			exec(expand(var["say"]))
			set_timer("timer", 2, function()
				send("dazuo " .. tonumber(dazuo))
			end)
		else
			set_timer("timer", 2, function()
				send("dazuo " .. tonumber(dazuo))
			end)
		end
	end
end)

add_alias("standard_dazuo_2", function(params) --第二套标准2 打坐alias 没有lian force
	local maxqi = var["maxqi"] or 100
	local do_stop = var["do_stop"] or 0
	if do_stop == 0 then
		local dazuo = var["dazuo"] or maxqi / 4 or 1000
		if var["say"] and var["say"] ~= "" then
			exec("yun jing;yun qi;hp " .. var["char_id"] .. ";@say")
			set_timer("timer", 2, function()
				send("dazuo " .. tonumber(dazuo))
			end)
		elseif var["say"] and var["say"] == "" then
			exec("yun jing;yun qi;hp " .. var["char_id"])
			set_timer("timer", 2, function()
				send("dazuo " .. tonumber(dazuo))
			end)
		end
	end
end)

--(2),标准打坐trigger
----------------------------------------------------------------
--> 你凝神静气，盘坐下来，运一口内家真气游走全身。

add_trigger("dazuo_1",
	"^[ > ]*你(?:手捏绣花针|凝神静气|盘膝而坐，运起八荒|运起纯阳神通|坐下来运气用功|盘膝而坐，双目紧闭，深深吸一口气|运起玄天无极|席地而坐，五心向天|五心向天|随意坐下|随意一站|手捏剑诀|收敛心神|轻轻的吸一口气|气运丹田，将体内毒素|屏息静气|盘膝坐下，席地而坐|盘膝而坐，形神合一|盘膝坐下，双手合十|盘膝坐下，垂目合什|盘膝坐下，闭目合什|盘膝坐下，暗运内力|盘膝而坐，运使九阳|盘膝而坐，双手垂于|盘膝而坐，双目紧闭|盘腿坐下，双目微闭|慢慢盘膝而坐|抉弃杂念|学着图中怪僧的姿势照做)",
	function(params)
		--你盘膝而坐，形神合一，暗运「冷泉神功」，将冷泉内劲游走全身经络。
		--	local aa=os.time()
		--echo("\n开始:"..C.W..aa)
		unset_timer("timer")
		del_timer("input")
		local maxneili = var["maxneili"] or 1000
		local neili_request = var["neili_request"] or 190
		local neili = var["neili"] or 1000
		local check_neilifull = math.min(2 * maxneili, neili_request * maxneili / 100)
		check_neilifull = check_neilifull - 100
		check_neilifull = tonumber(check_neilifull)
		--			echo(neili..":"..check_neilifull)
		var["neilifull"] = var["neilifull"] or 0
		if var["neilifull"] == 2 then
			var["neilifull"] = 1
		elseif neili > check_neilifull then
			var["neilifull"] = 1
		else
			var["neilifull"] = 0
		end
	end)
--> 你将内息游走全身，但觉全身舒畅，内力充沛无比。

add_trigger("dazuo_2",
	"^[ > ]*(.*)(?:双手分使，灵活异常，好象变成了两个人似的！你|你|过了片刻，你|一条)(?:已将「冷泉内劲」|将紫气|只觉真力运转顺畅|将纯阳神通功运行完毕|将北冥真气在体内运行|只觉神元|真气在|运功完毕，站了起来。|一个周天行将|吸气入丹田，真气|双眼微闭|慢慢收气|将周身内息|将真气|将内息|将寒冰真气|呼翕九阳，抱一含元|感觉自己已经将玄|感觉毒素越转越快|感到自己和天地|分开双手|极冷的冰线)",
	function(params)
		if not (string.find(params[1], "【") or string.find(params[1], "说道") or string.find(params[1], "告诉你") or string.find(params[1], "回答你")) then
			exec("after_dazuo")
		end
	end)

add_trigger("dazuo_3", "^[ > ]*你的内力修为已经(?:达到圆满之境。|无法靠打坐来提升了。)", function(params)
	var["neilifull"] = 2
end
)
add_trigger("dazuo_4", "^[ > ]*你没有那么多的气来产生内息运行全身经脉。", function(params)
	local hurtqi = var["hurtqi"] or 100
	unset_timer("timer")
	if hurtqi < 50 then
		wait(3, function()
			exec("after_dazuo")
		end)
	else
		wait(3, function()
			open_dazuo()
			exec("do_dazuo")
		end)
	end
end)
add_trigger("dazuo_5", "^[ > ]*你现在精不够，无法控制内息的流动！", function(params)
	local hurtjing = var["hurtjing"] or 100
	unset_timer("timer")
	if hurtjing < 70 then
		exec("after_dazuo")
	else
		wait(3, function()
			open_dazuo()
			exec("do_dazuo")
		end)
	end
end)
add_trigger("dazuo_6", "^[ > ]*你必须先用.*选择你要用的特殊内功。", function(params)
	local jifa_force = var["jifa_force"] or ""
	check_busy2(function()
		send("jifa all")
		send("jifa force " .. jifa_force)
		open_dazuo()
		exec("do_dazuo")
	end)
end)
add_trigger("dazuo_7", "^[ > ]*你至少需要(\\S+)点的气来打坐！", function(params)
	var["dazuo"] = trans(params[1]) + 10
	open_dazuo()
	exec("do_dazuo")
end)
add_trigger("dazuo_8", "^[ > ]*由于实战经验不足，阻碍了你的「(\\S+)」进步！", function(params)
	if var["skills_jifaname"] and var["skills_jifaname"]["force"] and string.find(var["skills_jifaname"]["force"], params[1]) then
		var["lian_force"] = 2
		var["lianforceoverpot"] = var["maxpot"] or 100
		--changejob 下次开启
	end
end)

add_trigger("dazuo_9", "^[ > ]*(?:这里可不是让你提高内力的地方|这里可不是让你提高精力的地方。)", function(params)
	send("north")
	send("south")
	send("south")
	open_dazuo()
	exec("do_dazuo")
end)
add_trigger("dazuo_10", "^[ > ]*你刚施用过内功，不能马上打坐。", function(params)

end)
add_trigger("dazuo_11", "^[ > ]*这里不准战斗，也不准打坐。", function(params)
	check_place(function()
		open_dazuo()
		exec("do_dazuo")
	end)
end)



--(3),打开和关闭打坐trigger的函数
----------------------------------------------------------------
--打开
function open_dazuo()
	open_trigger("dazuo_1")
	open_trigger("dazuo_2")
	open_trigger("dazuo_3")
	open_trigger("dazuo_4")
	open_trigger("dazuo_5")
	open_trigger("dazuo_6")
	open_trigger("dazuo_7")
	open_trigger("dazuo_8")
	open_trigger("dazuo_9")
	open_trigger("dazuo_10")
	open_trigger("dazuo_11")
end

--关闭
function close_dazuo()
	Print("停止打坐！")
	unset_timer("timer")
	close_trigger("dazuo_1")
	close_trigger("dazuo_2")
	close_trigger("dazuo_3")
	close_trigger("dazuo_4")
	close_trigger("dazuo_5")
	close_trigger("dazuo_6")
	close_trigger("dazuo_7")
	close_trigger("dazuo_8")
	close_trigger("dazuo_9")
	close_trigger("dazuo_10")
	close_trigger("dazuo_11")
end

close_dazuo() --保持trigger常闭

--(4),set_dazuo 的函数
--比如set_dazuo ask_job_xueshan
--代码中set_dazuo("ask_job_xueshan") 来执行这个函数
----------------------------------------------------------------
function set_dazuo(setting)
	local setting = setting or ""
	if setting == "" or setting == "none" then
		close_dazuo()
		add_alias("go_dazuo", function() end)
		add_alias("do_dazuo", function() end)
		add_alias("after_dazuo", function() end)
	elseif setting == "short_wait" then --打坐短暂等待
		add_alias("go_dazuo", function()
			open_dazuo()          --开启打坐触发
			send("set 积蓄")
			check_busy(function()
				check_place(function()
					exec("standard_dazuo")
				end)
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo_2")
		end)
		add_alias("after_dazuo", function()
			close_dazuo()
			exec("yun_powerup none")
			check_busy2(function()
				exec("time -s;cha;jifa;score;cond;i;yun qi;hp " .. var["char_id"])
			end)
		end)
	elseif setting == "wait_full" then --打坐短暂等待
		add_alias("go_dazuo", function()
			open_dazuo()         --开启打坐触发
			send("set 积蓄")
			check_busy(function()
				check_place(function()
					exec("standard_dazuo")
				end)
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo_2")
		end)

		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				--	yun_powerup(function()
				exec("yun_powerup none")
				check_busy2(function()
					exec("time -s;cha;jifa;cond;i;score;hp " .. var["char_id"])
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "ask_job_xueshan" then --雪山job ask 为啥lua没有switch？
		add_alias("go_dazuo", function()
			open_dazuo()               --开启打坐触发
			send("set 积蓄")
			check_busy(function()
				if var["fast_prepare"] ~= nil and var["fast_prepare"] == 1 and var["chuanbeiwan"] > 0 then --设置了任务之前吃药full 内力
					exec("eat chuanbei wan")
				end
				send("eu")
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				open_trigger("xueshan_1")
				local ado = var["ado"] or 0
				if 1 == 0 and ado == 1 then
					send("ado cond|time -s|exp")
				else
					send("cond")
					send("time -s")
					send("exp")
					--		send("wd")
				end
				check_busy(function()
					send("wd")
					check_busy(function()
						send("ask bao xiang about job")
					end)
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "ask_job_xuncheng" then --巡城
		add_alias("go_dazuo", function()
			open_dazuo()                --开启打坐触发
			send("unset 积蓄")
			check_busy(function()
				send("wu")
				wait(5, function()
					exec("after_dazuo")
				end)
				--			exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			open_trigger("xuncheng_1")
			send("exp")
			send("time -s")
			--			send("ed")
			check_busy(function()
				send("ed")
				send("ask zhu danchen about 巡城")
			end)
		end)
	elseif setting == "xuncheng_task_ok" then --巡城
		add_alias("go_dazuo", function()
			open_dazuo()                --开启打坐触发
			send("unset 积蓄")
			check_busy(function()
				send("wu")
				wait(5, function()
					exec("after_dazuo")
				end)
				--			exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			del_trigger("xuncheng_7") --删除让你task ok的触发
			open_trigger("xuncheng_3")
			open_trigger("xuncheng_4")
			open_trigger("xuncheng_5")
			send("exp")
			send("time -s")
			--			send("ed")
			check_busy(function()
				send("ed")
				send("task ok")
			end)
		end)
	elseif setting == "xuncheng_dazuo" then --巡城
		add_alias("go_dazuo", function()
			open_dazuo()              --开启打坐触发
			send("unset 积蓄")
			check_busy(function()
				exec("yun jingli;hp " .. var["char_id"] .. ";standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("yun jingli;hp " .. var["char_id"] .. ";standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local jingli = var["jingli"] or 100
			if jingli > 100 then
				exec("do_job")
			else
				exec("do_dazuo")
			end
		end)
	elseif setting == "ask_job_changlebang" then --长乐帮
		add_alias("go_dazuo", function()
			open_dazuo()                   --开启打坐触发
			send("set 积蓄")
			check_busy(function()
				send("s")
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				open_trigger("changlebang_1")
				local ado = var["ado"] or 0
				if 1 == 0 and ado == 1 then
					send("ado cond|time -s|exp")
				else
					send("cond")
					send("exp")
					send("time -s")
					--			send("n")
				end
				check_busy(function()
					send("n")
					send("ask bei haishi about job")
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "ask_fangqi_xueshan" then --xs雪山job放弃 fangqi
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			check_busy(function()
				send("eu")
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				open_trigger("xueshan_1")
				--		send("wd")
				check_busy(function()
					send("wd")
					check_busy(function()
						send("ask bao xiang about 失败")
					end)
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "ask_job_songshan" then --嵩山
		add_alias("go_dazuo", function()
			open_dazuo()                --开启打坐触发
			send("set 积蓄")
			check_busy(function()
				send("sd")
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				open_trigger("songshan_1")
				local ado = var["ado"] or 0
				if 1 == 0 and ado == 1 then
					send("ado cond|time -s|exp")
				else
					exec("cond;time -s;exp")
				end
				check_busy(function()
					exec("nu;ask zuo lengchan about job")
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "songshan_qing" then --ss qing
		add_alias("go_dazuo", function()
			open_dazuo()             --开启打坐触发
			var["do_stop"] = 0
			send("set 积蓄")
			check_busy(function()
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				send("cond")
				send("time -s")
				check_busy(function()
					exec("yun jing;yun qi;yun jingli;qing " .. var["killer_id"])
					--				exec("qing "..var["killer_id"])
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "ask_job_gaibang" then --丐帮吴长老帮
		add_alias("go_dazuo", function()
			open_dazuo()               --开启打坐触发
			send("set 积蓄")
			check_busy(function()
				send("out")
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				open_trigger("gaibang_1")
				local ado = var["ado"] or 0
				if 1 == 0 and ado == 1 then
					send("ado cond|time -s|exp")
				else
					exec("cond;time -s;exp")
				end
				check_busy(function()
					send("enter")
					send("ask wu zhanglao about job")
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "ask_fangqi_gaibang" then --gaibang job放弃 fangqi
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			check_busy(function()
				send("out")
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				open_trigger("gaibang_1")
				send("enter")
				exec("ask wu zhanglao about fangqi")
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "ask_job_wudang" then --武当wd_ask
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			if var["fast_prepare"] ~= nil and var["fast_prepare"] == 1 and var["chuanbeiwan"] > 0 then --设置了任务之前吃药full 内力
				exec("eat chuanbei wan")
			end
			send("out")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				open_trigger("wudang_1")
				local ado = var["ado"] or 0
				if 1 == 0 and ado == 1 then
					send("ado cond|time -s|exp")
				else
					exec("cond;time -s;exp")
				end
				check_busy(function()
					send("cond")
					--send("time -s")
					open_trigger("wudang_1")
					exes("enter;askk song yuanqiao about job", 2)
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "ask_job_huashan" then --华山hs ask job
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			send("n")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				open_trigger("huashan_1")
				local ado = var["ado"] or 0
				if 1 == 0 and ado == 1 then
					send("ado cond|exp")
				else
					exec("cond;exp")
				end
				check_busy(function()
					send("s")
					send("ask yue buqun about 惩恶扬善")
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "prepare_hs2" then --prepare_hs2
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			if var["fast_prepare"] ~= nil and var["fast_prepare"] == 1 and var["chuanbeiwan"] > 0 then --设置了任务之前吃药full 内力
				exec("eat chuanbei wan")
			end
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["neilifull"] == 1 then
					close_dazuo()
					check_busy2(function()
						exec("unwield_weapon;wield_weapon;job_accept")
					end)
				else
					exec("standard_dazuo")
				end
			end
		end)
	elseif setting == "ask_job_guanfu" then --官府jie 文书 hs ask job
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			--		send("n")
			exec("yun qi")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				send("cond")
				send("exp")
				send("time -s")
				--			send("s")
				open_trigger("guanfu_1")
				send("look wanted list")
				--		send("ask yue buqun about 惩恶扬善")
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "dujiang" then --渡江dujiang
		open_dazuo()
		add_alias("go_dazuo", function()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			do_stop = var["do_stop"] or 0
			var["dujiang"] = var["dujiang"] or 1
			if do_stop == 0 then
				if var["dujiang"] == 2 then
					local ado = var["ado"] or 0
					if 1 == 0 and ado == 1 then
						send("ado set brief|yell boat|yell 上崖|yell 下崖|enter")
						exec("do_dazuo")
					else
						exec("set brief;yell boat;yell 上崖;yell 下崖;enter;do_dazuo")
					end
				else
					local ado = var["ado"] or 0
					if 1 == 0 and ado == 1 then
						send("ado dujiang|duhe|zong")
					else
						send("dujiang")
						send("duhe")
						send("zong")
					end
				end
			end
		end)
	elseif setting == "yideng_jump_front" then --一灯跳梁yideng jump front
		open_dazuo()
		add_alias("go_dazuo", function()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				local neilifull = var["neilifull"] or 0
				if neilifull > 0 then
					close_dazuo()
					send("jump front")
				else
					exec("standard_dazuo")
				end
			end
		end)
	elseif setting == "yell_boat" then --坐船yell boat
		open_dazuo()
		add_alias("go_dazuo", function()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["dujiang"] = var["dujiang"] or 1
			if do_stop == 0 then
				send("set brief")
				send("yell boat")
				send("enter")
				exec("do_dazuo")
			end
		end)
	elseif setting == "prepare" then --准备任务prepare
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			if var["fast_prepare"] ~= nil and var["fast_prepare"] == 1 and var["chuanbeiwan"] > 0 then --设置了任务之前吃药full 内力
				exec("eat chuanbei wan")
			end

			check_place(function() --check_place函数检查是否安全区然后dazuo
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["neilifull"] == 1 then
					close_dazuo()
					exec("changejob")
				else
					exec("standard_dazuo")
				end
			end
		end)
	elseif setting == "ask_job_songxin" then --songxin 送信ask job
		add_alias("go_dazuo", function()
			open_dazuo()
			check_busy(function()
				send("set 积蓄")
				send("w")
				send("w")
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			exec("standard_dazuo")
		end)
		add_alias("after_dazuo", function()
			local neilifull = var["neilifull"] or 0
			if neilifull > 0 then
				close_dazuo()
				send("cond")
				send("exp")
				send("time -s")
				open_trigger("songxin")
				check_busy2(function()
					send("e")
					send("e")
					send("ask zhu wanli about job")
				end)
			else
				exec("standard_dazuo")
			end
		end)
	elseif setting == "prepare_sx2" then --prepare_sx2
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			if var["fast_prepare"] ~= nil and var["fast_prepare"] == 1 and var["chuanbeiwan"] > 0 then --设置了任务之前吃药full 内力
				exec("eat chuanbei wan")
			end
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["neilifull"] == 1 then
					close_dazuo()
					exec("do_job_sx")
				else
					exec("standard_dazuo")
				end
			end
		end)
	elseif setting == "cisha" then --cisha
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["neilifull"] == 1 then
					close_dazuo()
					exec("yun qi;yun jingli;yun jing;do_job_cisha")
				else
					exec("get gold;standard_dazuo")
				end
			end
		end)
	elseif setting == "smy" then --smy
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["smy_over"] == true then
					close_dazuo()
					exec("changejob")
				else
					if var["neilifull"] == 1 then
						close_dazuo()
						exec("yun qi;yun jingli;yun jing;do_job_smy")
					else
						exec("get gold;standard_dazuo")
					end
				end
			end
		end)
	elseif setting == "slhs" then --slhs
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["slhs_over"] == true then
					close_dazuo()
					exec("changejob")
				else
					if var["neilifull"] == 1 then
						close_dazuo()
						exec("yun qi;yun jingli;yun jing;do_job_slhs")
					else
						exec("get gold;standard_dazuo")
					end
				end
			end
		end)
	elseif setting == "swxy" then --swxy
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["swxy_over"] == true then
					close_dazuo()
					exec("changejob")
				else
					if var["neilifull"] == 1 then
						close_dazuo()
						exec("yun qi;yun jingli;yun jing;do_job_swxy")
					else
						exec("standard_dazuo")
					end
				end
			end
		end)
	elseif setting == "nextquest" then --nextquest
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["neilifull"] == 1 then
					close_dazuo()
					exec("yun qi;yun jingli;yun jing;nextquest")
				else
					exec("get gold;standard_dazuo")
				end
			end
		end)
	elseif setting == "huoshao" then --huoshao
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["neilifull"] == 1 then
					close_dazuo()
					exec("yun qi;yun jingli;yun jing;do_job_huoshao")
				else
					exec("standard_dazuo")
				end
			end
		end)
	elseif setting == "do_job_wudang_2" then -- do_job_wudang_2 武当一次不成功再次打坐尝试
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["neilifull"] == 1 then
					close_dazuo()
					exec("do_job_wudang_2")
				else
					exec("standard_dazuo")
				end
			end
		end)
	elseif setting == "do_job_xueshan_2" then --雪山失败再次杀
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			if do_stop == 0 then
				if var["neilifull"] == 1 then
					close_dazuo()
					exec("do_job_xueshan_2")
				else
					exec("standard_dazuo")
				end
			end
		end)
	elseif setting == "yili_wait" then --伊犁关门打坐等待
		add_alias("go_dazuo", function()
			open_dazuo()
			send("unset 积蓄")
			send("look")
			exec("standard_dazuo")
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				send("look")
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			var["idle"] = 0
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				if string.find(var["roomname"], "城中心") then
					if string.find(var["roomexit"], "south") then
						close_dazuo()
						send("south")
						exec("gps")
					else
						send("unset 积蓄")
						send("look")
						exec("standard_dazuo")
					end
				else
					exec("gps")
				end
			end
		end)
	elseif setting == "lingwu" then --领悟lingwu
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			send("south")
			send("south")
			exec("standard_dazuo_2")
		end)
		add_alias("do_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			if var["do_stop"] == 0 then
				exec("standard_dazuo_2") -- 第二套打坐alias
			end
		end)
		add_alias("after_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			--var["noeat"]=var["noeat"] or 0
			if var["do_stop"] == 0 then
				if math.min(var["water"], var["food"]) < 20 and var["noeat"] ~= 1 then
					exec("after_usepot")
				else
					var["neilifull"] = var["neilifull"] or 0
					if var["neilifull"] > 0 then
						close_dazuo()
						send("north")
						send("north")
						exec("lingwu_skills")
					else
						exec("standard_dazuo_2")
					end
				end
			end
		end)
	elseif setting == "changejob" then
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			send("south")
			send("south")
			exec("standard_dazuo_2")
		end)
		add_alias("do_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			if var["do_stop"] == 0 then
				exec("standard_dazuo_2") -- 第二套打坐alias
			end
		end)
		add_alias("after_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			--var["noeat"]=var["noeat"] or 0
			if var["do_stop"] == 0 then
				if math.min(var["water"], var["food"]) < 20 and var["noeat"] ~= 1 then
					exec("after_usepot")
				else
					var["neilifull"] = var["neilifull"] or 0
					if var["neilifull"] > 0 then
						close_dazuo()
						exec("changejob")
					else
						exec("standard_dazuo_2")
					end
				end
			end
		end)
	elseif setting == "lianxi" then
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			send("south")
			send("south")
			exec("standard_dazuo_2")
		end)
		add_alias("do_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			if var["do_stop"] == 0 then
				exec("standard_dazuo_2") -- 第二套打坐alias
			end
		end)
		add_alias("after_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			--var["noeat"]=var["noeat"] or 0
			if var["do_stop"] == 0 then
				if math.min(var["water"], var["food"]) < 20 and var["noeat"] ~= 1 then
					exec("after_usepot")
				else
					var["neilifull"] = var["neilifull"] or 0
					if var["neilifull"] > 0 then
						close_dazuo()
						exec("lian_skills")
					else
						exec("standard_dazuo_2")
					end
				end
			end
		end)
	elseif setting == "xuexi" then
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			check_place(function()
				exec("standard_dazuo_2")
			end)
		end)
		add_alias("do_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			if var["do_stop"] == 0 then
				exec("standard_dazuo_2") -- 第二套打坐alias
			end
		end)
		add_alias("after_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			if var["do_stop"] == 0 then
				if math.min(var["water"], var["food"]) < 20 and var["noeat"] ~= 1 then
					exec("changejob")
				else
					var["neilifull"] = var["neilifull"] or 0
					if var["neilifull"] > 0 then
						close_dazuo()
						exec("go_xuexi")
					else
						exec("standard_dazuo_2")
					end
				end
			end
		end)
	elseif setting == "heath" or setting == "health" then --可能health写错了。。。不去找了
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			check_place(function()
				exec("standard_dazuo")
			end)
		end)
		add_alias("do_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			if var["do_stop"] == 0 then
				exec("standard_dazuo")
			end
		end)
		add_alias("after_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			if var["do_stop"] == 0 then
				if math.min(var["water"], var["food"]) < 20 and var["noeat"] ~= 1 then
					exec("changejob")
				else
					exec("yun_heal")
				end
			end
		end)
	elseif setting == "tdh_heath" then --tdh job heal天地会疗伤
		add_alias("go_dazuo", function()
			open_dazuo()
			send("set 积蓄")
			check_place(function()
				exec("standard_dazuo_2")
			end)
		end)
		add_alias("do_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			if var["do_stop"] == 0 then
				exec("standard_dazuo_2")
			end
		end)
		add_alias("after_dazuo", function()
			var["do_stop"] = var["do_stop"] or 0
			if var["do_stop"] == 0 then
				if math.min(var["water"], var["food"]) < 20 and var["noeat"] ~= 1 then
					--exec("changejob")
					exec("yun_heal")
				else
					exec("yun_heal")
				end
			end
		end)
	elseif setting == "tdh_dazuo" then --天地会打坐
		add_alias("go_dazuo", function()
			local neili = var["neili"] or 1
			if neili < 1000 then
				open_dazuo()
				send("set 积蓄")
				exec("standard_dazuo_2")
			else
				check_busy(function()
					exec("yun qi;yun jingli;yun jing;do_tdh_find")
				end)
			end
		end)
		add_alias("do_dazuo", function()
			local do_stop = var["do_stop"] or 0
			if do_stop == 0 then
				exec("standard_dazuo_2")
			end
		end)
		add_alias("after_dazuo", function()
			local do_stop = var["do_stop"] or 0
			var["neilifull"] = var["neilifull"] or 0
			local hurtqi = var["hurtqi"] or 100
			if do_stop == 0 then
				if hurtqi < 35 then
					exec("do_quit quit")
				elseif var["neilifull"] == 1 then
					close_dazuo()
					check_busy(function()
						exec("yun qi;yun jingli;yun jing;do_tdh_find") --do_tdh_find
					end)
				else
					exec("get gold from corpse;get silver from corpse;get silver from corpse 2;standard_dazuo")
				end
			end
		end)
	else -- else

	end
end

--check_palce() 检查打坐地点
function after_check_place()
end

function check_place(checking)
	--close_dazuo()
	after_check_place = checking

	add_trigger("check_place_1", "^[ > ]*(?:卧室不能吐纳，会影响别人休息。|你还是专心拱猪吧！|这里不准战斗，也不准吐纳。|你无法静下心来修炼。)", function(params)
		del_timer("timer")
		exec("random_move")
		set_timer("timer", 0.3, function()
			send("tuna 0")
		end)
	end)
	add_trigger("check_place_2", "^[ > ]*你的内功修行还没有高到能如此精确控制！", function(params)
		--		echo("test3")
		del_timer("timer")
		del_trigger("check_place_1")
		del_trigger("check_place_2")
		after_check_place()
	end)
	set_timer("timer", 0.5, function()
		--	send("haha")
		send("tuna 0")
	end)
end

--你无法静下心来修炼。
--你的内功修行还没有高到能如此精确控制！
--这里不准战斗，也不准吐纳。
